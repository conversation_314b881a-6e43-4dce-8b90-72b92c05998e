import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/widget/folder_view_data.dart';

class CommonFolderItemWidget extends StatelessWidget {
  final FolderViewData viewData;
  final bool isTablet;

  const CommonFolderItemWidget({
    required this.viewData,
    required this.isTablet,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      alignment: Alignment.topCenter,
      padding: EdgeInsets.zero,
      onPressed: viewData.onTap,
      child: _buildFolderItemWidget(context),
    );
  }

  /// Builds a folder item widget with folder information and optional error indicator
  Widget _buildFolderItemWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildFolderIcon(context),
          const SizedBox(width: 12),
          _buildFolderInfo(context),
          _buildFolderOptions(context),
        ],
      ),
    );
  }

  /// Builds the folder icon with optional error indicator
  Widget _buildFolderIcon(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(isTablet ? 16 : 16.w),
          width: isTablet ? 64 : 64.w,
          height: isTablet ? 64 : 64.w,
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: SvgPicture.asset(viewData.icon),
        ),
        if (viewData.isUnsyncedNote == true)
          Positioned(
            bottom: 0,
            right: 0,
            child: SvgPicture.asset(
              Assets.icons.icFolderError,
              width: 16,
              height: 16,
            ),
          ),
      ],
    );
  }

  /// Builds the folder information section (name and note count)
  Widget _buildFolderInfo(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonText(
            viewData.folderName,
            style: TextStyle(
              fontSize: isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainPrimary,
            ),
            maxLines: 1,
          ),
          _buildCountsInfo(context),
        ],
      ),
    );
  }

  /// Builds the counts information (notes count and subfolders count)
  Widget _buildCountsInfo(BuildContext context) {
    final hasNotes = viewData.numberOfNotes > 0;
    final hasSubfolders = viewData.subfoldersCount > 0;

    if (!hasNotes && !hasSubfolders) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        if (hasNotes) ...[
          CommonText(
            "${viewData.numberOfNotes} ${S.current.notes}",
            style: TextStyle(
              fontSize: isTablet ? 14 : 12.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
        ],
        if (hasNotes && hasSubfolders) ...[
          CommonText(
            " • ",
            style: TextStyle(
              fontSize: isTablet ? 14 : 12.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
        ],
        if (hasSubfolders) ...[
          CommonText(
            "${viewData.subfoldersCount} folders",
            style: TextStyle(
              fontSize: isTablet ? 14 : 12.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds the folder options button (if applicable)
  Widget _buildFolderOptions(BuildContext context) {
    final editNameController = TextEditingController();
    if (!viewData.showOptions) {
      return const SizedBox.shrink();
    }

    // Create a temporary FolderModel for the FolderMoreOptions widget
    final tempFolder = FolderModel(
      folderName: viewData.folderName,
      backendId: viewData.id,
    );

    return FolderMoreOptions(
      folder: tempFolder,
      editFolderController: editNameController,
      icon: Assets.icons.icFolderMore,
      isInDetailPage: false,
      onEditFolderConfirm:
          viewData.onEditFolderConfirm,
      onDeleteConfirm: viewData.onDeleteConfirm,
    );
  }
}
