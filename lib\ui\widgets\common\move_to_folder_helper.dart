import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// Helper class for managing folder move operations and UI
class MoveToFolderHelper {
  /// Shows the bottom sheet for moving folders and notes
  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
    VoidCallback? onFolderMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => _MoveToFolderBottomSheet(
        foldersToBeMovedIds: foldersToBeMovedIds,
        jumpToFolderId: jumpToFolderId,
        notesToBeMoved: notesToBeMoved,
        onFolderMoved: onFolderMoved,
      ),
    );
  }
}

/// Validation utilities for folder move operations
class _FolderMoveValidator {
  /// Check if the target folder is invalid for moving
  static bool isInvalidTarget(MoveFolderViewModel targetFolder, List<String>? foldersToBeMovedIds) {
    if (foldersToBeMovedIds == null || foldersToBeMovedIds.isEmpty) {
      return false; // No folders to move, so any target is valid
    }

    // Check if target is one of the folders being moved
    if (foldersToBeMovedIds.contains(targetFolder.backendId)) {
      return true;
    }

    // Check if target is a descendant of any folder being moved
    for (final folderId in foldersToBeMovedIds) {
      if (_isDescendantOf(targetFolder.backendId, folderId)) {
        return true;
      }
    }

    return false;
  }

  /// Check if a folder is a descendant of another folder
  static bool _isDescendantOf(String potentialDescendant, String ancestorId) {
    final allFolders = HiveFolderService.getAllFolders();
    final folderMap = <String, FolderModel>{};
    for (final folder in allFolders) {
      folderMap[folder.backendId] = folder;
    }

    final folder = folderMap[potentialDescendant];
    if (folder == null || folder.parentFolderId == null) return false;

    if (folder.parentFolderId == ancestorId) return true;

    return _isDescendantOf(folder.parentFolderId!, ancestorId);
  }
}

/// Error handler for move folder operations
class _MoveToFolderErrorHandler {
  static void handleError(String? errorMessage) {
    debugPrint('MoveFolderCubit error: $errorMessage');
    String displayMessage = errorMessage ?? 'Unknown error';

    // Parse specific error messages for better user experience
    if (displayMessage.contains('Cannot move folder into itself')) {
      displayMessage = 'Cannot move folder into itself';
    } else if (displayMessage.contains('Cannot move folder into its own subfolder')) {
      displayMessage = 'Cannot move folder into its own subfolder';
    } else if (displayMessage.contains('404')) {
      displayMessage = 'Folder not found or operation not allowed';
    } else if (displayMessage.contains('Failed to move folder')) {
      displayMessage = 'Failed to move folder. Please try again.';
    }

    CommonDialogs.showToast(
      displayMessage,
      gravity: ToastGravity.BOTTOM,
      length: Toast.LENGTH_LONG,
    );
  }

  static void showInvalidSelectionToast() {
    CommonDialogs.showToast(
      'Cannot move folder into itself or its subfolder',
      gravity: ToastGravity.BOTTOM,
      length: Toast.LENGTH_SHORT,
    );
  }

  static void showSuccessToast() {
    CommonDialogs.showToast(
      'Folder moved successfully',
      gravity: ToastGravity.BOTTOM,
      length: Toast.LENGTH_SHORT,
    );
  }
}

/// Main bottom sheet widget for moving folders and notes
class _MoveToFolderBottomSheet extends StatelessWidget {
  final List<String>? foldersToBeMovedIds;
  final String? jumpToFolderId;
  final List<NoteModel>? notesToBeMoved;
  final VoidCallback? onFolderMoved;

  const _MoveToFolderBottomSheet({
    this.foldersToBeMovedIds,
    this.jumpToFolderId,
    this.notesToBeMoved,
    this.onFolderMoved,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final cubit = GetIt.instance.get<MoveFolderCubit>();
        cubit.initialize(jumpToFolderId: jumpToFolderId);
        return cubit;
      },
      child: BlocConsumer<MoveFolderCubit, MoveFolderState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          if (state.isLoading) {
            return const _LoadingWidget();
          }
          return _MoveToFolderContent(
            foldersToBeMovedIds: foldersToBeMovedIds,
            notesToBeMoved: notesToBeMoved,
          );
        },
      ),
    );
  }

  void _handleStateChanges(BuildContext context, MoveFolderState state) {
    switch (state.oneShotEvent) {
      case MoveFolderOneShotEvent.folderMoved:
        Navigator.pop(context);
        _MoveToFolderErrorHandler.showSuccessToast();
        onFolderMoved?.call();
        break;
      case MoveFolderOneShotEvent.subfolderCreated:
        Navigator.pop(context);
        break;
      case MoveFolderOneShotEvent.error:
        _MoveToFolderErrorHandler.handleError(state.errorMessage);
        break;
      default:
        break;
    }
  }
}

/// Loading widget for the bottom sheet
class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const SafeArea(
      child: SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }
}

/// Main content widget for the move to folder bottom sheet
class _MoveToFolderContent extends StatelessWidget {
  final List<String>? foldersToBeMovedIds;
  final List<NoteModel>? notesToBeMoved;

  const _MoveToFolderContent({
    this.foldersToBeMovedIds,
    this.notesToBeMoved,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: EdgeInsets.only(
          left: context.isTablet ? 16 : 16.w,
          right: context.isTablet ? 16 : 16.w,
          top: context.isTablet ? 16 : 16.h,
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        height: MediaQuery.of(context).size.height * 0.95,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const _HeaderWidget(),
            _FolderListWidget(foldersToBeMovedIds: foldersToBeMovedIds),
            _ActionButtonsWidget(
              foldersToBeMovedIds: foldersToBeMovedIds,
              notesToBeMoved: notesToBeMoved,
            ),
          ],
        ),
      ),
    );
  }
}

/// Header widget with title and close button
class _HeaderWidget extends StatelessWidget {
  const _HeaderWidget();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CommonText(
            S.current.add_folder,
            style: TextStyle(
              fontSize: context.isTablet ? 22 : 20.sp,
              fontWeight: FontWeight.w600,
              color: context.colorScheme.mainPrimary,
            ),
          ),
        ),
        GestureDetector(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            Assets.icons.icCloseWhite,
            width: context.isTablet ? 32 : 24.w,
            height: context.isTablet ? 32 : 24.w,
            fit: BoxFit.contain,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }
}

/// Folder list widget that displays the folder hierarchy
class _FolderListWidget extends StatelessWidget {
  final List<String>? foldersToBeMovedIds;

  const _FolderListWidget({
    this.foldersToBeMovedIds,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        return Expanded(
          child: ListView(
            shrinkWrap: true,
            children: state.rootFolders
                .map((folder) => MoveFolderTile(
                      folder: folder,
                      foldersToBeMovedIds: foldersToBeMovedIds,
                      onTap: (f) => _handleFolderTap(context, f),
                      onToggleExpansion: (folderId) {
                        context.read<MoveFolderCubit>().toggleFolderExpansion(folderId);
                      },
                    ))
                .toList(),
          ),
        );
      },
    );
  }

  void _handleFolderTap(BuildContext context, MoveFolderViewModel folder) {
    if (!_FolderMoveValidator.isInvalidTarget(folder, foldersToBeMovedIds)) {
      context.read<MoveFolderCubit>().selectFolder(folder);
    } else {
      _MoveToFolderErrorHandler.showInvalidSelectionToast();
    }
  }
}

/// Action buttons widget with Create and Move buttons
class _ActionButtonsWidget extends StatelessWidget {
  final List<String>? foldersToBeMovedIds;
  final List<NoteModel>? notesToBeMoved;

  const _ActionButtonsWidget({
    this.foldersToBeMovedIds,
    this.notesToBeMoved,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Row(
            children: [
              Expanded(
                child: _CreateButton(selectedFolder: state.selectedFolder),
              ),
              AppConstants.kSpacingItemW10,
              Expanded(
                child: _MoveButton(
                  selectedFolder: state.selectedFolder,
                  foldersToBeMovedIds: foldersToBeMovedIds,
                  notesToBeMoved: notesToBeMoved,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Create button widget for creating subfolders
class _CreateButton extends StatelessWidget {
  final MoveFolderViewModel? selectedFolder;

  const _CreateButton({
    this.selectedFolder,
  });

  @override
  Widget build(BuildContext context) {
    return AppCommonButton(
      backgroundColor: context.colorScheme.mainSecondary,
      borderRadius: BorderRadius.circular(24.r),
      height: context.isTablet ? 44 : 44.h,
      onPressed: selectedFolder == null ? null : () => _handleCreateSubfolder(context),
      textWidget: CommonText(
        S.current.create,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          color: context.colorScheme.mainPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<void> _handleCreateSubfolder(BuildContext context) async {
    final selectedFolderId = selectedFolder!.backendId;
    final controller = TextEditingController();

    showCreateFolderDialog(
      context,
      controller: controller,
      onPressed: () async {
        try {
          await context.read<MoveFolderCubit>().createSubfolder(
                name: controller.text.trim(),
                parentFolderId: selectedFolderId,
              );
          if (context.mounted) {
            Navigator.pop(context);
          }
        } catch (e) {
          debugPrint('Error creating subfolder: $e');
          if (context.mounted) {
            Navigator.pop(context);
          }
        }
      },
      onClosed: () => controller.dispose(),
      title: S.current.create_new_folder,
      contentButton: S.current.create,
      hintText: S.current.required,
      initialValue: '',
    );
  }
}

/// Move button widget for moving folders and notes
class _MoveButton extends StatelessWidget {
  final MoveFolderViewModel? selectedFolder;
  final List<String>? foldersToBeMovedIds;
  final List<NoteModel>? notesToBeMoved;

  const _MoveButton({
    this.selectedFolder,
    this.foldersToBeMovedIds,
    this.notesToBeMoved,
  });

  @override
  Widget build(BuildContext context) {
    final isDisabled = selectedFolder == null ||
        _FolderMoveValidator.isInvalidTarget(selectedFolder!, foldersToBeMovedIds);

    return AppCommonButton(
      backgroundColor: context.colorScheme.mainBlue,
      borderRadius: BorderRadius.circular(24.r),
      height: context.isTablet ? 44 : 44.h,
      onPressed: isDisabled ? null : () => _handleMove(context),
      textWidget: CommonText(
        S.current.move,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          color: context.colorScheme.mainPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<void> _handleMove(BuildContext context) async {
    await context.read<MoveFolderCubit>().moveFoldersAndNotes(
          folderIds: foldersToBeMovedIds ?? [],
          notes: notesToBeMoved ?? [],
          targetFolderId: selectedFolder!.backendId,
        );
  }
}

/// Widget for displaying individual folder tiles in the hierarchy
class MoveFolderTile extends StatelessWidget {
  final MoveFolderViewModel folder;
  final Function(MoveFolderViewModel folder)? onTap;
  final Function(String folderId)? onToggleExpansion;
  final List<String>? foldersToBeMovedIds;
  final int level;

  const MoveFolderTile({
    Key? key,
    required this.folder,
    this.onTap,
    this.onToggleExpansion,
    this.foldersToBeMovedIds,
    this.level = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        final isSelected = state.selectedFolder?.backendId == folder.backendId;
        final isInvalid = _FolderMoveValidator.isInvalidTarget(folder, foldersToBeMovedIds);

        return Column(
          children: [
            _FolderTileContent(
              folder: folder,
              isSelected: isSelected,
              isInvalid: isInvalid,
              level: level,
              onTap: onTap,
              onToggleExpansion: onToggleExpansion,
            ),
            if (folder.isExpanded) ..._buildSubfolders(),
          ],
        );
      },
    );
  }

  List<Widget> _buildSubfolders() {
    return folder.subfolders
        .map((sub) => MoveFolderTile(
              folder: sub,
              onTap: onTap,
              onToggleExpansion: onToggleExpansion,
              foldersToBeMovedIds: foldersToBeMovedIds,
              level: level + 1,
            ))
        .toList();
  }
}

/// Content widget for individual folder tile
class _FolderTileContent extends StatelessWidget {
  final MoveFolderViewModel folder;
  final bool isSelected;
  final bool isInvalid;
  final int level;
  final Function(MoveFolderViewModel folder)? onTap;
  final Function(String folderId)? onToggleExpansion;

  const _FolderTileContent({
    required this.folder,
    required this.isSelected,
    required this.isInvalid,
    required this.level,
    this.onTap,
    this.onToggleExpansion,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.only(left: 16.w * level),
      tileColor: _getTileColor(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      leading: _buildLeadingIcons(context),
      title: _buildTitle(context),
      onTap: () => _handleTap(),
    );
  }

  Color? _getTileColor(BuildContext context) {
    if (isSelected) {
      return context.colorScheme.mainSecondary;
    } else if (isInvalid) {
      return context.colorScheme.mainGray.withOpacity(0.3);
    }
    return null;
  }

  Widget _buildLeadingIcons(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (folder.subfolders.isNotEmpty) _buildExpansionIcon(context),
        _buildFolderIcon(context),
      ],
    );
  }

  Widget _buildExpansionIcon(BuildContext context) {
    return GestureDetector(
      onTap: () => onToggleExpansion?.call(folder.backendId),
      child: SvgPicture.asset(
        folder.isExpanded ? Assets.icons.icExpandMore : Assets.icons.icExpandLess,
        width: context.isTablet ? 24 : 16.w,
        height: context.isTablet ? 24 : 16.w,
        fit: BoxFit.contain,
        colorFilter: isInvalid ? _getInvalidColorFilter(context) : null,
      ),
    );
  }

  Widget _buildFolderIcon(BuildContext context) {
    return SvgPicture.asset(
      Assets.icons.icFlipFolderMini,
      width: context.isTablet ? 48 : 32.w,
      height: context.isTablet ? 48 : 32.w,
      fit: BoxFit.contain,
      colorFilter: isInvalid ? _getInvalidColorFilter(context) : null,
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      folder.folderName,
      style: TextStyle(
        color: isInvalid ? context.colorScheme.mainGray.withOpacity(0.5) : null,
      ),
    );
  }

  ColorFilter _getInvalidColorFilter(BuildContext context) {
    return ColorFilter.mode(
      context.colorScheme.mainGray.withOpacity(0.5),
      BlendMode.srcIn,
    );
  }

  void _handleTap() {
    debugPrint('MoveFolderTile: User selected folder ${folder.folderName} (${folder.backendId})');
    onTap?.call(folder);
  }
}
